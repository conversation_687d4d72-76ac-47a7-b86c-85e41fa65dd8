package com.lirong.installation.facilities.domain;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.lirong.common.annotation.AutoId;
import com.lirong.common.annotation.Excel;
import com.lirong.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 基地主要设施对象 installation_facilities
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
public class InstallationFacilities extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @AutoId
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 军事设施ID
     */
    private Long installationId;

    /**
     * 军事设施编码
     */
    @Excel(name = "军事设施编码")
    private String installationCode;

    /**
     * 设施类型
     */
    @Excel(name = "设施类型")
    private String facilityType;

    /**
     * 设施名称
     */
    @Excel(name = "设施名称")
    private String facilityName;

    /**
     * 用途
     */
    @Excel(name = "用途")
    private String purpose;

    /**
     * 简介
     */
    @Excel(name = "简介")
    private String description;

    /**
     * 数据来源
     */
    @Excel(name = "数据来源")
    private String source;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @Excel(name = "删除标志", readConverterExp = "0=代表存在,1=代表删除")
    private Integer isDelete;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setInstallationId(Long installationId) {
        this.installationId = installationId;
    }

    public Long getInstallationId() {
        return installationId;
    }

    public void setInstallationCode(String installationCode) {
        this.installationCode = installationCode;
    }

    public String getInstallationCode() {
        return installationCode;
    }

    public void setFacilityType(String facilityType) {
        this.facilityType = facilityType;
    }

    public String getFacilityType() {
        return facilityType;
    }

    public void setFacilityName(String facilityName) {
        this.facilityName = facilityName;
    }

    public String getFacilityName() {
        return facilityName;
    }

    public void setPurpose(String purpose) {
        this.purpose = purpose;
    }

    public String getPurpose() {
        return purpose;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getSource() {
        return source;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("installationId", getInstallationId())
                .append("installationCode", getInstallationCode())
                .append("facilityType", getFacilityType())
                .append("facilityName", getFacilityName())
                .append("purpose", getPurpose())
                .append("description", getDescription())
                .append("source", getSource())
                .append("isDelete", getIsDelete())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
