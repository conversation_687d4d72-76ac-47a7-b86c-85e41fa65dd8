package com.lirong.installation.facilities.service.impl;

import java.util.List;

import com.lirong.common.utils.DateUtils;
import com.lirong.common.utils.ShiroUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lirong.installation.facilities.mapper.InstallationFacilitiesMapper;
import com.lirong.installation.facilities.domain.InstallationFacilities;
import com.lirong.installation.facilities.service.InstallationFacilitiesService;
import com.lirong.common.core.text.Convert;

/**
 * 基地主要设施Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Service
public class InstallationFacilitiesServiceImpl implements InstallationFacilitiesService {
    @Autowired
    private InstallationFacilitiesMapper installationFacilitiesMapper;

    /**
     * 查询基地主要设施
     *
     * @param id 基地主要设施ID
     * @return 基地主要设施
     */
    @Override
    public InstallationFacilities selectInstallationFacilitiesById(Long id) {
        return installationFacilitiesMapper.selectInstallationFacilitiesById(id);
    }

    /**
     * 查询基地主要设施列表
     *
     * @param installationFacilities 基地主要设施
     * @return 基地主要设施
     */
    @Override
    public List<InstallationFacilities> selectInstallationFacilitiesList(InstallationFacilities installationFacilities) {
        return installationFacilitiesMapper.selectInstallationFacilitiesList(installationFacilities);
    }

    /**
     * 新增基地主要设施
     *
     * @param installationFacilities 基地主要设施
     * @return 结果
     */
    @Override
    public int insertInstallationFacilities(InstallationFacilities installationFacilities) {
        installationFacilities.setCreateBy(ShiroUtils.getUserName());
        installationFacilities.setCreateTime(DateUtils.getNowDate());
        return installationFacilitiesMapper.insertInstallationFacilities(installationFacilities);
    }

    /**
     * 修改基地主要设施
     *
     * @param installationFacilities 基地主要设施
     * @return 结果
     */
    @Override
    public int updateInstallationFacilities(InstallationFacilities installationFacilities) {
        installationFacilities.setUpdateBy(ShiroUtils.getUserName());
        installationFacilities.setUpdateTime(DateUtils.getNowDate());
        return installationFacilitiesMapper.updateInstallationFacilities(installationFacilities);
    }

    /**
     * 删除基地主要设施对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteInstallationFacilitiesByIds(String ids) {
        String loginName = ShiroUtils.getUserName();
        return installationFacilitiesMapper.deleteInstallationFacilitiesByIds(Convert.toStrArray(ids), loginName);
    }
}
