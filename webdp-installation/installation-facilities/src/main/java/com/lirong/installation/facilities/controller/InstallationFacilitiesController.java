package com.lirong.installation.facilities.controller;

import java.util.List;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.lirong.common.annotation.Log;
import com.lirong.common.enums.BusinessType;
import com.lirong.installation.facilities.domain.InstallationFacilities;
import com.lirong.installation.facilities.service.InstallationFacilitiesService;
import com.lirong.common.core.controller.BaseController;
import com.lirong.common.core.domain.AjaxResult;
import com.lirong.common.utils.poi.ExcelUtil;
import com.lirong.common.core.page.TableDataInfo;

/**
 * 基地主要设施Controller
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Controller
@RequestMapping("/installation/facilities")
public class InstallationFacilitiesController extends BaseController {
    private String prefix = "installation/facilities";

    @Autowired
    private InstallationFacilitiesService installationFacilitiesService;

    @RequiresPermissions("installation:facilities:view")
    @GetMapping("/{installationCode}")
    public String facilities(@PathVariable("installationCode") String installationCode, ModelMap mmap) {
        mmap.put("installationCode", installationCode);
        return prefix + "/facilities";
    }

    /**
     * 查询基地主要设施列表
     */
    @RequiresPermissions("installation:facilities:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(InstallationFacilities installationFacilities) {
        startPage();
        List<InstallationFacilities> list = installationFacilitiesService.selectInstallationFacilitiesList(installationFacilities);
        return getDataTable(list);
    }

    /**
     * 新增基地主要设施
     */
    @GetMapping("/add/{installationCode}")
    public String add(@PathVariable("installationCode") String installationCode, ModelMap mmap) {
        mmap.put("installationCode", installationCode);
        return prefix + "/add";
    }

    /**
     * 新增保存基地主要设施
     */
    @RequiresPermissions("installation:facilities:add")
    @Log(title = "基地主要设施", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(InstallationFacilities installationFacilities) {
        return toAjax(installationFacilitiesService.insertInstallationFacilities(installationFacilities));
    }

    /**
     * 修改基地主要设施
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        InstallationFacilities installationFacilities = installationFacilitiesService.selectInstallationFacilitiesById(id);
        mmap.put("installationFacilities", installationFacilities);
        return prefix + "/edit";
    }

    /**
     * 修改保存基地主要设施
     */
    @RequiresPermissions("installation:facilities:edit")
    @Log(title = "基地主要设施", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(InstallationFacilities installationFacilities) {
        return toAjax(installationFacilitiesService.updateInstallationFacilities(installationFacilities));
    }

    /**
     * 删除基地主要设施
     */
    @RequiresPermissions("installation:facilities:remove")
    @Log(title = "基地主要设施", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(installationFacilitiesService.deleteInstallationFacilitiesByIds(ids));
    }

    /**
     * 导出基地主要设施列表
     */
    @RequiresPermissions("installation:facilities:export")
    @Log(title = "基地主要设施", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(InstallationFacilities installationFacilities) {
        List<InstallationFacilities> list = installationFacilitiesService.selectInstallationFacilitiesList(installationFacilities);
        ExcelUtil<InstallationFacilities> util = new ExcelUtil<InstallationFacilities>(InstallationFacilities.class);
        return util.exportExcel(list, "facilities");
    }
}
