#!/usr/bin/env python3
"""
AI服务连接测试脚本
用于测试本地LLM服务是否正常运行
"""

import requests
import json
import time

def test_ai_service():
    """测试AI服务连接"""
    
    # 配置信息
    api_url = "http://192.168.0.222:8000/v1/chat/completions"
    model = "deepseek-ai/DeepSeek-V3"
    
    # 构建测试请求
    test_data = {
        "model": model,
        "messages": [
            {
                "role": "user", 
                "content": "Hello, this is a test message. Please respond with 'AI service is working correctly.'"
            }
        ],
        "max_tokens": 100,
        "temperature": 0.1
    }
    
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json",
        "Authorization": "Bearer sk-local-llm-key"
    }
    
    print(f"正在测试AI服务连接...")
    print(f"服务地址: {api_url}")
    print(f"模型: {model}")
    print("-" * 50)
    
    try:
        # 发送请求
        start_time = time.time()
        response = requests.post(
            api_url, 
            headers=headers, 
            json=test_data, 
            timeout=60
        )
        end_time = time.time()
        
        print(f"响应时间: {end_time - start_time:.2f} 秒")
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ AI服务连接成功!")
            print(f"响应内容: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"❌ AI服务返回错误状态码: {response.status_code}")
            print(f"错误内容: {response.text}")
            return False
            
    except requests.exceptions.ConnectTimeout:
        print("❌ 连接超时 - 请检查服务是否启动")
        return False
    except requests.exceptions.ConnectionError as e:
        print(f"❌ 连接错误 - 无法连接到服务: {e}")
        return False
    except requests.exceptions.Timeout:
        print("❌ 请求超时 - 服务响应时间过长")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def test_parsing_request():
    """测试人员信息解析请求"""
    
    api_url = "http://192.168.0.222:8000/v1/chat/completions"
    model = "deepseek-ai/DeepSeek-V3"
    
    # 构建解析测试请求
    test_bio = """John Smith is a Vice Admiral in the U.S. Navy. He was born in 1965 in New York. 
    He graduated from the Naval Academy in 1987 and has served in various positions including 
    Commander of the Pacific Fleet. He currently serves as Deputy Chief of Naval Operations."""
    
    prompt = f"""请从以下英文简介中提取人员信息，并以JSON格式返回。需要提取的字段包括：
- nameEn: 英文姓名
- nameCn: 中文姓名（根据英文名称翻译）
- militaryRank: 军衔（如Vice Adm.=中将，Admiral=上将，General=上将等，军衔不要包含军种信息）
- orgName: 所在机构
- post: 当前职务，职务中不要包含机构信息
- birthplace: 出生地
- graduatedUniversity: 毕业院校
- education: 最高学历
- degree: 最高学位
- birthdayYear: 出生年份（仅在简介中明确提及时填写，不要推测）
- birthdayMonth: 出生月份（两位数字，如01，仅在简介中明确提及时填写）
- birthdayDay: 出生日期（两位数字，如01，仅在简介中明确提及时填写）
- troopsCategory: 军兵种（navy=海军，army=陆军，air=空军，space_force=太空军，marines=海军陆战队，national_guard=国民警卫队，coastguard=海岸警卫队）
- peopleType: 人员类型（0102=作战指挥人员，0101=行政管理人员，0199=其他人员）
- gender: 性别（1=男，2=女）
- workStatus: 工作状态（固定填写"current"）
- educationExperience: 教育经历（每条记录一行，格式：时间-学校-专业）
- workExperience: 工作经历（每条记录一行，格式：时间-职务-机构）

英文简介：
{test_bio}

请直接返回JSON格式的结果，不要包含其他说明文字。"""
    
    test_data = {
        "model": model,
        "messages": [
            {
                "role": "user", 
                "content": prompt
            }
        ],
        "max_tokens": 4000,
        "temperature": 0.1
    }
    
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json",
        "Authorization": "Bearer sk-local-llm-key"
    }
    
    print("\n" + "=" * 50)
    print("正在测试人员信息解析功能...")
    print("=" * 50)
    
    try:
        start_time = time.time()
        response = requests.post(
            api_url, 
            headers=headers, 
            json=test_data, 
            timeout=120
        )
        end_time = time.time()
        
        print(f"解析响应时间: {end_time - start_time:.2f} 秒")
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 人员信息解析测试成功!")
            
            # 提取AI返回的内容
            if 'choices' in result and len(result['choices']) > 0:
                content = result['choices'][0]['message']['content']
                print(f"解析结果: {content}")
                
                # 尝试解析JSON
                try:
                    parsed_json = json.loads(content)
                    print("✅ JSON解析成功!")
                    print(f"解析后的数据: {json.dumps(parsed_json, indent=2, ensure_ascii=False)}")
                except json.JSONDecodeError as e:
                    print(f"⚠️ JSON解析失败: {e}")
                    print("原始内容可能包含额外文字")
            
            return True
        else:
            print(f"❌ 解析请求失败: {response.status_code}")
            print(f"错误内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 解析测试失败: {e}")
        return False

if __name__ == "__main__":
    print("AI服务连接测试工具")
    print("=" * 50)
    
    # 基础连接测试
    basic_test = test_ai_service()
    
    if basic_test:
        # 解析功能测试
        parsing_test = test_parsing_request()
        
        if parsing_test:
            print("\n🎉 所有测试通过! AI服务工作正常。")
        else:
            print("\n⚠️ 基础连接正常，但解析功能可能有问题。")
    else:
        print("\n❌ 基础连接测试失败，请检查AI服务配置。")
        print("\n故障排查建议:")
        print("1. 确认LLM服务在 http://192.168.0.222:8000 上运行")
        print("2. 检查网络连接")
        print("3. 确认服务端口8000是否开放")
        print("4. 检查防火墙设置")
