package com.lirong.personnel.common;

import com.lirong.personnel.common.config.AIConfig;
import com.lirong.personnel.common.service.IdwPeopleMainService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Map;

/**
 * AI服务连接测试类
 * 用于测试AI解析功能是否正常工作
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class AIServiceTest {

    @Autowired
    private IdwPeopleMainService idwPeopleMainService;

    @Autowired
    private AIConfig aiConfig;

    @Test
    public void testAIConfiguration() {
        System.out.println("=== AI配置信息测试 ===");
        System.out.println("API URL: " + aiConfig.getApiUrl());
        System.out.println("模型: " + aiConfig.getModel());
        System.out.println("最大Token: " + aiConfig.getMaxTokens());
        System.out.println("连接超时: " + aiConfig.getConnectTimeout() + "ms");
        System.out.println("读取超时: " + aiConfig.getReadTimeout() + "ms");
        System.out.println("温度参数: " + aiConfig.getTemperature());
    }

    @Test
    public void testAIParsingService() {
        System.out.println("=== AI解析服务测试 ===");
        
        String testBio = "John Smith is a Vice Admiral in the U.S. Navy. He was born in 1965 in New York. " +
                "He graduated from the Naval Academy in 1987 and has served in various positions including " +
                "Commander of the Pacific Fleet. He currently serves as Deputy Chief of Naval Operations.";

        try {
            long startTime = System.currentTimeMillis();
            Map<String, Object> result = idwPeopleMainService.parseEnglishBiography(testBio);
            long endTime = System.currentTimeMillis();

            System.out.println("✅ AI解析成功!");
            System.out.println("解析耗时: " + (endTime - startTime) + "ms");
            System.out.println("解析结果:");
            
            for (Map.Entry<String, Object> entry : result.entrySet()) {
                System.out.println("  " + entry.getKey() + ": " + entry.getValue());
            }

        } catch (Exception e) {
            System.err.println("❌ AI解析失败: " + e.getMessage());
            e.printStackTrace();
            
            // 提供故障排查建议
            System.out.println("\n故障排查建议:");
            System.out.println("1. 检查AI服务是否在 " + aiConfig.getApiUrl() + " 运行");
            System.out.println("2. 检查网络连接是否正常");
            System.out.println("3. 检查服务端口是否开放");
            System.out.println("4. 查看应用日志获取详细错误信息");
        }
    }

    @Test
    public void testAIParsingWithChineseBio() {
        System.out.println("=== AI解析（包含中文翻译）测试 ===");
        
        String testBio = "Admiral Michael Johnson is the current Chief of Naval Operations. " +
                "Born in California in 1960, he graduated from the U.S. Naval Academy in 1982. " +
                "He has extensive experience in submarine operations and strategic planning.";

        try {
            long startTime = System.currentTimeMillis();
            Map<String, Object> result = idwPeopleMainService.parseEnglishBiography(testBio);
            long endTime = System.currentTimeMillis();

            System.out.println("✅ AI解析（含翻译）成功!");
            System.out.println("解析耗时: " + (endTime - startTime) + "ms");
            
            // 特别检查中文翻译结果
            if (result.containsKey("profileCn")) {
                System.out.println("✅ 中文翻译成功:");
                System.out.println("  " + result.get("profileCn"));
            } else {
                System.out.println("⚠️ 未生成中文翻译");
            }
            
            // 检查关键字段
            String[] keyFields = {"nameEn", "nameCn", "militaryRank", "post", "orgName"};
            System.out.println("\n关键字段检查:");
            for (String field : keyFields) {
                if (result.containsKey(field)) {
                    System.out.println("  ✅ " + field + ": " + result.get(field));
                } else {
                    System.out.println("  ❌ " + field + ": 未提取");
                }
            }

        } catch (Exception e) {
            System.err.println("❌ AI解析失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testAIServiceConnectivity() {
        System.out.println("=== AI服务连通性测试 ===");
        
        // 使用简单的测试内容
        String simpleTest = "Test message for connectivity check.";
        
        try {
            System.out.println("正在测试AI服务连接...");
            Map<String, Object> result = idwPeopleMainService.parseEnglishBiography(simpleTest);
            
            System.out.println("✅ AI服务连接正常!");
            System.out.println("服务响应正常，可以进行解析操作。");
            
        } catch (RuntimeException e) {
            System.err.println("❌ AI服务连接失败: " + e.getMessage());
            
            // 分析错误类型并提供建议
            String errorMsg = e.getMessage().toLowerCase();
            if (errorMsg.contains("connection") || errorMsg.contains("连接")) {
                System.out.println("\n🔧 连接问题排查:");
                System.out.println("1. 检查LLM服务是否启动: http://192.168.0.222:8000");
                System.out.println("2. 检查网络连接");
                System.out.println("3. 检查防火墙设置");
            } else if (errorMsg.contains("timeout") || errorMsg.contains("超时")) {
                System.out.println("\n🔧 超时问题排查:");
                System.out.println("1. 检查服务器性能");
                System.out.println("2. 增加超时时间配置");
                System.out.println("3. 检查网络延迟");
            } else if (errorMsg.contains("404")) {
                System.out.println("\n🔧 接口问题排查:");
                System.out.println("1. 检查API路径是否正确");
                System.out.println("2. 确认服务支持 /v1/chat/completions 接口");
            } else {
                System.out.println("\n🔧 其他问题排查:");
                System.out.println("1. 查看详细日志");
                System.out.println("2. 检查服务配置");
                System.out.println("3. 验证API密钥");
            }
        } catch (Exception e) {
            System.err.println("❌ 测试过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
