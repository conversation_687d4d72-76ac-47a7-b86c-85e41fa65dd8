<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lirong.installation.facilities.mapper.InstallationFacilitiesMapper">
    
    <resultMap type="com.lirong.installation.facilities.domain.InstallationFacilities" id="InstallationFacilitiesResult">
        <result property="id"    column="id"    />
        <result property="installationId"    column="installation_id"    />
        <result property="installationCode"    column="installation_code"    />
        <result property="facilityType"    column="facility_type"    />
        <result property="facilityName"    column="facility_name"    />
        <result property="purpose"    column="purpose"    />
        <result property="description"    column="description"    />
        <result property="source"    column="source"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <select id="selectInstallationFacilitiesList" parameterType="com.lirong.installation.facilities.domain.InstallationFacilities" resultMap="InstallationFacilitiesResult">
        SELECT
            *
        FROM
            installation_facilities
        WHERE
            is_delete = 0
        <if test="installationCode != null  and installationCode != ''"> AND installation_code = #{installationCode}</if>
        <if test="facilityType != null  and facilityType != ''"> AND facility_type = #{facilityType}</if>
        <if test="facilityName != null  and facilityName != ''"> AND facility_name LIKE concat( '%', #{facilityName}, '%' )</if>
        <if test="purpose != null  and purpose != ''"> AND purpose LIKE concat( '%', #{purpose}, '%' )</if>
    </select>
    
    <select id="selectInstallationFacilitiesById" parameterType="Long" resultMap="InstallationFacilitiesResult">
        SELECT
            *
        FROM
            installation_facilities
        WHERE
            is_delete = 0
            AND id = #{id}
    </select>
        
    <insert id="insertInstallationFacilities" parameterType="com.lirong.installation.facilities.domain.InstallationFacilities">
        insert into installation_facilities
        <trim prefix="(" suffix=")" suffixOverrides=",">
            is_delete,
            <if test="id != null">id,</if>
            <if test="installationId != null">installation_id,</if>
            <if test="installationCode != null">installation_code,</if>
            <if test="facilityType != null">facility_type,</if>
            <if test="facilityName != null">facility_name,</if>
            <if test="purpose != null">purpose,</if>
            <if test="description != null">description,</if>
            <if test="source != null and source != ''">source,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            0,
            <if test="id != null">#{id},</if>
            <if test="installationId != null">#{installationId},</if>
            <if test="installationCode != null">#{installationCode},</if>
            <if test="facilityType != null">#{facilityType},</if>
            <if test="facilityName != null">#{facilityName},</if>
            <if test="purpose != null">#{purpose},</if>
            <if test="description != null">#{description},</if>
            <if test="source != null and source != ''">#{source},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateInstallationFacilities" parameterType="com.lirong.installation.facilities.domain.InstallationFacilities">
        update installation_facilities
        <trim prefix="SET" suffixOverrides=",">
            <if test="installationCode != null">installation_code = #{installationCode},</if>
            <if test="facilityType != null">facility_type = #{facilityType},</if>
            <if test="facilityName != null">facility_name = #{facilityName},</if>
            <if test="purpose != null">purpose = #{purpose},</if>
            <if test="description != null">description = #{description},</if>
            <if test="source != null and source != ''">source = #{source},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <!--根据军事设施编码删除-->
    <update id="deleteInstallationFacilitiesByInstallationCodes">
        UPDATE installation_facilities
        SET update_by = #{loginName},
        update_time = sysdate(),
        is_delete = 1
        WHERE
            is_delete = 0
            AND installation_code IN
        <foreach item="installationCode" collection="installationCodes" open="(" separator="," close=")">
            #{installationCode}
        </foreach>
    </update>

    <update id="deleteInstallationFacilitiesByIds">
        update installation_facilities
        SET update_by = #{loginName},
        update_time = sysdate(),
        IS_DELETE = 1
        WHERE id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

</mapper>
