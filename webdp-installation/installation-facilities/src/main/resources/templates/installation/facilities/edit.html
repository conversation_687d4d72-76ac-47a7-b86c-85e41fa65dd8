<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('修改基地主要设施')"/>
    <th:block th:include="include :: select2-css"/>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-facilities-edit" th:object="${installationFacilities}">
        <input name="id" th:field="*{id}" type="hidden">
        <input name="installationCode" th:field="*{installationCode}" type="hidden">
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">设施类型：</label>
            <div class="col-sm-8">
                <select name="facilityType" class="form-control m-b"
                        th:with="type=${@dict.getType('installation_facility_type')}" required>
                    <option value="" style="color: #b6b6b6" disabled selected>选择设施类型</option>
                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"
                            th:field="*{facilityType}"></option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">设施名称：</label>
            <div class="col-sm-8">
                <input name="facilityName" th:field="*{facilityName}" class="form-control" type="text" required>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">用途：</label>
            <div class="col-sm-8">
                <input name="purpose" th:field="*{purpose}" class="form-control" type="text">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">简介：</label>
            <div class="col-sm-8">
                <textarea name="description" class="form-control" rows="6">[[*{description}]]</textarea>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">数据来源：</label>
            <div class="col-sm-8">
                <input name="source" th:field="*{source}" class="form-control" required>
            </div>
        </div>
    </form>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: select2-js"/>
<script th:inline="javascript">
    let prefix = ctx + "installation/facilities";
    $("#form-facilities-edit").validate({
        focusCleanup: true
    });

    function submitHandler() {
        if ($.validate.form()) {
            $.operate.save(prefix + "/edit", $('#form-facilities-edit').serialize());
        }
    }
</script>
</body>
</html>
