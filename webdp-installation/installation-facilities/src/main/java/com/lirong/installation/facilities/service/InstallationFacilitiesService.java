package com.lirong.installation.facilities.service;

import java.util.List;
import com.lirong.installation.facilities.domain.InstallationFacilities;

/**
 * 基地主要设施Service接口
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
public interface InstallationFacilitiesService {
    /**
     * 查询基地主要设施
     *
     * @param id 基地主要设施ID
     * @return 基地主要设施
     */
    public InstallationFacilities selectInstallationFacilitiesById(Long id);

    /**
     * 查询基地主要设施列表
     *
     * @param installationFacilities 基地主要设施
     * @return 基地主要设施集合
     */
    public List<InstallationFacilities> selectInstallationFacilitiesList(InstallationFacilities installationFacilities);

    /**
     * 新增基地主要设施
     *
     * @param installationFacilities 基地主要设施
     * @return 结果
     */
    public int insertInstallationFacilities(InstallationFacilities installationFacilities);

    /**
     * 修改基地主要设施
     *
     * @param installationFacilities 基地主要设施
     * @return 结果
     */
    public int updateInstallationFacilities(InstallationFacilities installationFacilities);

    /**
     * 批量删除基地主要设施
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteInstallationFacilitiesByIds(String ids);
}
