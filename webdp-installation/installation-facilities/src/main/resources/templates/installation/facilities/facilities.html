<div class="container-div">
    <input type="hidden" value="基地主要设施列表">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="form-facilities">
                <div class="select-list">
                    <input type="hidden" name="installationCode" th:value="${installationCode}">
                    <ul>
                        <li>
                            <label style="width: 100px;">设施类型：</label>
                            <select name="facilityType" th:with="type=${@dict.getType('installation_facility_type')}">
                                <option value="">所有</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </li>
                        <li>
                            <label style="width: 100px;">设施名称：</label>
                            <input type="text" name="facilityName"/>
                        </li>
                        <li>
                            <label style="width: 100px;">用途：</label>
                            <input type="text" name="purpose"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search('form-facilities', 'bootstrap-table-facilities')"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset('form-facilities')"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar-facilities" role="group">
            <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="installation:facilities:add">
                <i class="fa fa-plus"></i> 添加
            </a>
            <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="installation:facilities:edit">
                <i class="fa fa-edit"></i> 修改
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="installation:facilities:remove">
                <i class="fa fa-remove"></i> 删除
            </a>
            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="installation:facilities:export">
                <i class="fa fa-download"></i> 导出
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table-facilities"></table>
        </div>
    </div>
</div>

<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    let prefix = ctx + "installation/facilities";
    let installationCode = [[${installationCode}]];

    $(function () {
        let options = {
            id: "bootstrap-table-facilities",          // 指定表格ID
            toolbar: "toolbar-facilities",   // 指定工具栏ID
            formId: "form-facilities",
            url: prefix + "/list",
            createUrl: prefix + "/add/" + installationCode,
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            modalName: "基地主要设施",
            uniqueId: 'id',
            columns: [{
                checkbox: true
            },
            {
                align: 'center',
                title: "序号",
                width: 60,
                formatter: function (value, row, index) {
                    return $.table.serialNumber(index);
                }
            },
            {
                align: 'center',
                field: 'facilityType',
                title: '设施类型',
                width: 120,
                formatter: function (value, row, index) {
                    return $.table.selectDictLabel(facilityTypeDatas, value);
                }
            },
            {
                align: 'center',
                field: 'facilityName',
                title: '设施名称',
                width: 200
            },
            {
                align: 'center',
                field: 'purpose',
                title: '用途',
                width: 150
            },
            {
                align: 'center',
                field: 'description',
                title: '简介',
                width: 300,
                formatter: function (value, row, index) {
                    return $.table.tooltip(value, 50);
                }
            },
            {
                align: 'center',
                field: 'source',
                title: '数据来源',
                width: 150,
                formatter: function (value, row, index) {
                    return $.table.tooltip(value, 20);
                }
            },
            {
                title: '操作',
                align: 'center',
                formatter: function (value, row, index) {
                    let actions = [];
                    actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                    actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                    return actions.join('');
                }
            }]
        };
        $.table.init(options);
    });

    let facilityTypeDatas = [[${@dict.getType('installation_facility_type')}]];
    let editFlag = [[${@permission.hasPermi('installation:facilities:edit')}]] ? '' : 'disabled';
    let removeFlag = [[${@permission.hasPermi('installation:facilities:remove')}]] ? '' : 'disabled';
</script>
